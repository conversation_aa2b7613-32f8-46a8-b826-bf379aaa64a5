const path = require('path');
const webpack = require('webpack');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');


const glob = require('glob');

// Environment configuration utility
function getEnvironmentConfig() {
    let envUrls = process.env.URLS;

    try {
        if (envUrls) {
            envUrls = JSON.parse(envUrls);
        }
    } catch (err) {
        console.log(err);
        console.error('ENV URLS has unsupported format ' + envUrls);
        envUrls = null;
    }

    const urls = envUrls || require('./config/urls').urls;
    const { paymentHub: paymentHubConfig } = require('./config/paymentHub');

    return { urls, paymentHubConfig };
}

const { urls, paymentHubConfig } = getEnvironmentConfig();

const entryFiles = glob.sync([
    './frontend_admin/sport-wrench-admin.module.js',
    './frontend_admin/**/*.js',
    './assets/styles/main.scss',
    './assets/styles/admin.scss',
], { absolute: true });

module.exports = (env, argv) => {
    const isProd = argv.mode === 'production';
    return {
        entry: entryFiles,

        output: {
            filename: '[name].js',
            path: path.resolve(__dirname, 'dist'),
            publicPath: '/',
        },
        module: {
            rules: [
                {
                    test: /\.js$/,
                    exclude: /node_modules/,
                    use: {
                        loader: 'babel-loader',
                        options: {
                            sourceMap: false,
                            presets: ['@babel/preset-env'],
                        },
                    },
                },
                {
                    test: /\.scss$/,
                    use: [
                        MiniCssExtractPlugin.loader,
                        'css-loader',
                        {
                            loader: 'sass-loader',
                            options: {
                                implementation: require('sass'),
                                sourceMap: true,
                                sassOptions: {
                                    includePaths: ['assets/bower_components', 'assets/styles'],
                                },
                            },
                        },
                    ],
                },
                {
                    test: /\.css$/,
                    use: [MiniCssExtractPlugin.loader, 'css-loader'],
                },
                {
                    test: /\.html$/,
                    use: ['html-loader'],
                },
            ],
        },
        plugins: [
            new CleanWebpackPlugin(),
            new CopyWebpackPlugin({
                patterns: [
                    { from: './assets/bower_components', to: 'bower_components' },
                    { from: './assets/js', to: 'js' },
                    { from: './assets/images', to: 'images' },
                    { from: './assets/favicon.ico', to: '' },
                    { context: './frontend_admin/', from: '**/*.html', to: '[path][name][ext]' },
                ],
            }),
            new MiniCssExtractPlugin({
                filename: '[name].css',
            }),
            new webpack.DefinePlugin({
                'process.env': {
                    NODE_ENV: JSON.stringify(isProd ? 'production' : 'development'),
                    HOME_PAGE_URL: JSON.stringify(urls.home_page.baseUrl),
                    MAIN_APP_URL: JSON.stringify(urls.main_app.baseUrl),
                    ESW_URL: JSON.stringify(urls.esw.baseUrl),
                    ESW_NEW_URL: JSON.stringify(urls.esw_new.baseUrl),
                    SWT_URL: JSON.stringify(urls.swt.baseUrl),
                    SCORES_APP_URL: JSON.stringify(urls.scores.baseUrl),
                    SALES_HUB_URL: JSON.stringify(urls.salesHub.baseUrl),
                    PAYMENT_HUB_API_HOST: JSON.stringify(paymentHubConfig.apiUrl),
                    PAYMENT_HUB_PUBLISHABLE_KEY: JSON.stringify(paymentHubConfig.publicKey),
                    ENV: JSON.stringify(isProd ? 'production' : 'development'),
                },
            }),
        ],
        optimization: {
            splitChunks: {
                cacheGroups: {
                    default: false,
                    vendors: false,
                },
            },
            runtimeChunk: false,
            minimize: isProd,
            minimizer: [
                new TerserPlugin({
                    exclude: /bower_components/,
                }),
            ],
        },
        resolve: {
            extensions: ['.js', '.scss', '.css'],
        },
        devServer: {
            static: [
                {
                    directory: path.join(__dirname, 'dist'),
                    publicPath: '/'
                },
                {
                    directory: path.join(__dirname, 'bower_components'),
                    publicPath: '/bower_components/'
                },
            ],
            proxy: [{
                context: ['/api'],
                target: `http://localhost:3000`,
                changeOrigin: true,
            }],
            port: 8087,
            hot: true
        },
        stats: {
            warnings: false,
        },
        ignoreWarnings: [
            {
                module: /sass-loader/,
                message: /Module Warning/,
            },
        ],
    };
};
