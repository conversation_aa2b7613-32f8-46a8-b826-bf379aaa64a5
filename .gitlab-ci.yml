stages:
  - test
  - deploy
  - reload

deploy:node:
  stage: deploy
  only:
    refs:
      - master
    changes:
      - api/**/*
      - api/**/**/*
      - config/**/*
      - userconfig/routes/**/*
      - scheduler/**/*
      - scheduler/**/**/*
      - sw-utils/**/*
      - views/**/*
      - app.js
      - officials-schedule-export.js
      - ecosystem-prod.json
      - deploy/**/*
      - .gitlab-ci.yml
      - Dockerfile
  script:
    - ansible-playbook -l marc-aws-sw deploy/node.yml

deploy:node_npm:
  stage: deploy
  only:
    refs:
      - master
    changes:
      - package.json
      - package-lock.json
  script:
    - ansible-playbook -l marc-aws-sw deploy/node_npm_i.yml
  dependencies:
    - deploy:node

reload:prod:
    stage: reload
    only:
        refs:
        - master
        changes:
        - package.json
        - package-lock.json
        - api/**/*
        - api/**/**/*
        - config/**/*
        - userconfig/routes/**/*
        - scheduler/**/*
        - scheduler/**/**/*
        - sw-utils/**/*
        - views/**/*
        - app.js
        - officials-schedule-export.js
        - ecosystem-prod.json
        - deploy/**/*
        - .gitlab-ci.yml
        - Dockerfile
    script:
      - ansible-playbook -l marc-aws-sw deploy/pm2_reload.yml -e "REDIS_URL=$REDIS_URL_PROD" -e "HOST_PORT=$HOST_PORT_PROD" -e "EMAIL_REDIS_URL=$EMAIL_REDIS_URL_PROD" -e "SW_DB=$SW_DB_PROD" -e "NODE_ENV=production" -e "LOG_PG_CS=$LOG_PG_CS_PROD" -e "LOG_APP_ID=$LOG_APP_ID"
    dependencies:
      - deploy:node
      - deploy:node_npm

deploy:static:
  stage: deploy
  only:
    refs:
      - master
    changes:
      - bower.json
      - .bowerrc
      - tasks/**/*
      - Gruntfile.js
      - assets/**/*
      - assets/**/**/*
      - frontend/**/*
      - frontend/**/**/*
  script:
    - "docker run --rm -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'npm i --legacy-peer-deps && bower install --allow-root && npm rebuild node-sass;'"
    - "EMAIL_DB=$EMAIL_DB_PROD SW_DB=$SW_DB_PROD docker run --rm --env 'EMAIL_DB' --env 'SW_DB' -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'grunt prod'"
    - ansible-playbook -l marc-aws-sw deploy/static.yml -e "SW_DB=$SW_DB_PROD"
  cache:
    key:
      files:
        - package-lock.json
        - bower.json
    paths:
      - node_modules/
      - bower_components/

deploy:static_events:
  stage: deploy
  only:
    refs:
      - master
    changes:
      - bower.json
      - .bowerrc
      - tasks/**/*
      - Gruntfile.js
      - assets/**/*
      - assets/**/**/*
      - frontend_event/**/*
      - frontend_event/**/**/*
  script:
    - "docker run --rm -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'npm i --legacy-peer-deps && bower install --allow-root && npm rebuild node-sass;'"
    - "EMAIL_DB=$EMAIL_DB_PROD SW_DB=$SW_DB_PROD docker run --rm --env 'EMAIL_DB' --env 'SW_DB' -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'grunt prod_esw;'"
    - ansible-playbook -l marc-aws-sw deploy/static_esw.yml -e "SW_DB=$SW_DB_PROD"
  cache:
    key:
      files:
        - package-lock.json
        - bower.json
    paths:
      - node_modules/
      - bower_components/

deploy:static_admin:
  stage: deploy
  only:
    refs:
      - master
    changes:
      - bower.json
      - .bowerrc
      - tasks/**/*
      - Gruntfile.js
      - assets/**/*
      - assets/**/**/*
      - frontend_admin/**/*
      - frontend_admin/**/**/*
  script:
    - "docker run --rm -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'npm i --legacy-peer-deps && bower install --allow-root && npm rebuild node-sass;'"
    - "EMAIL_DB=$EMAIL_DB_PROD SW_DB=$SW_DB_PROD docker run --rm --env 'EMAIL_DB' --env 'SW_DB' -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'grunt prod_asw;'"
    - ansible-playbook -l marc-aws-sw deploy/static_asw.yml -e "SW_DB=$SW_DB_PROD"
  cache:
    key:
      files:
        - package-lock.json
        - bower.json
    paths:
      - node_modules/
      - bower_components/

deploy:node_stage_npm:
  stage: deploy
  only:
    refs:
      - staging
    changes:
      - package.json
      - package-lock.json
  script:
    - ansible-playbook deploy/node_stage_npm_i.yml

deploy:node_stage:
  stage: deploy
  only:
    refs:
      - staging
    changes:
      - api/**/*
      - api/**/**/*
      - config/**/*
      - userconfig/routes/**/*
      - scheduler/**/*
      - scheduler/**/**/*
      - sw-utils/**/*
      - views/**/*
      - app.js
      - officials-schedule-export.js
      - ecosystem-dev.json
  script:
    - ansible-playbook -l marc-aws-sw-stage deploy/node_stage.yml
  dependencies:
    - deploy:node_stage_npm

deploy:static_stage:
  stage: deploy
  only:
    refs:
      - staging
    changes:
      - bower.json
      - .bowerrc
      - tasks/**/*
      - Gruntfile.js
      - assets/**/*
      - assets/**/**/*
      - frontend/**/*
      - frontend/**/**/*
  script:
    - "docker run --rm -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'npm i --legacy-peer-deps && bower install --allow-root && npm rebuild node-sass;'"
    - "docker run --rm -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'grunt prod'"
    - ansible-playbook -l marc-aws-sw-stage deploy/static_stage.yml -e "SW_DB=$SW_DB_PROD"
  cache:
    key:
      files:
        - package-lock.json
        - bower.json
    paths:
      - node_modules/
      - bower_components/

deploy:static_events_stage:
  stage: deploy
  only:
    refs:
      - staging
    changes:
      - bower.json
      - .bowerrc
      - tasks/**/*
      - Gruntfile.js
      - assets/**/*
      - assets/**/**/*
      - frontend_event/**/*
      - frontend_event/**/**/*
  script:
    - "docker run --rm -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'npm i --legacy-peer-deps && bower install --allow-root && npm rebuild node-sass;'"
    - "docker run --rm -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'grunt prod_esw;'"
    - ansible-playbook -l marc-aws-sw-stage deploy/static_esw_stage.yml -e "SW_DB=$SW_DB_PROD"
  cache:
    key:
      files:
        - package-lock.json
        - bower.json
    paths:
      - node_modules/
      - bower_components/

#deploy:static_admin_stage:
#  stage: deploy
#  only:
#    refs:
#      - staging
#    changes:
#      - bower.json
#      - .bowerrc
#      - tasks/**/*
#      - Gruntfile.js
#      - assets/**/*
#      - assets/**/**/*
#      - frontend_admin/**/*
#      - frontend_admin/**/**/*
#  script:
#    - "docker run --rm -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'npm i --legacy-peer-deps && bower install --allow-root && npm rebuild node-sass;'"
#    - "docker run --rm --env 'EMAIL_DB=postgres://sw:<EMAIL>:5432/emailqueue' --env 'SW_DB=postgres://sw:<EMAIL>:5432/sw' -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'grunt prod_asw;'"
#    - ansible-playbook -l marc-aws-sw-stage deploy/static_asw_stage.yml
#  cache:
#  key:
#    files:
#     - package-lock.json
#     - bower.json
#  paths:
#    - node_modules/
#    - bower_components/

test:migration:
  stage: test
  only:
    refs:
      - development
    changes:
      - db/migrations/main/*
  script:
    - "docker run --rm -v `pwd`:/app -w /app --env SW_DB=$TEST_DB_CONNECTION -u `id -u $USER`:`id -g $USER` --env HOME=. node:16 sh -c 'mkdir /app/logs && npm install knex --legacy-peer-deps && npm run migrate-main'"

test:node:
 only:
   refs:
     - development
   variables:
     - $SKIP_TESTS != "true"
 cache:
   key: "$CI_COMMIT_REF_SLUG"
   paths:
      - node_modules/
 resource_group: test
 stage: test
 script:
    - "docker run --rm -v `pwd`:/app -w /app --env REDIS_URL=$REDIS_URL_DEV --env EMAIL_REDIS_URL=$EMAIL_REDIS_URL_DEV --env TEST_NODE_ENV=development --env TEST_DB_CONNECTION --env SW_LOGGER_FILE_PATH=./ --env SW_DB=false -u `id -u $USER`:`id -g $USER` --env HOME=. node:16 sh -c 'mkdir /app/logs && mkdir -p /app/uploads/rosterImport && npm ci --legacy-peer-deps && npm run test'"

deploy:migration:
  stage: deploy
  only:
    refs:
      - development
    changes:
      - db/migrations/main/*
  script:
    - "docker run --rm -v `pwd`:/app -w /app --env SW_DB=$SW_DB_DEV -u `id -u $USER`:`id -g $USER` --env HOME=. node:16 sh -c 'mkdir /app/logs && npm install knex --legacy-peer-deps && npm run migrate-main'"

deploy:node_dev:
  stage: deploy
  only:
    refs:
      - development
    changes:
      - api/**/*
      - api/**/**/*
      - config/**/*
      - userconfig/routes/**/*
      - scheduler/**/*
      - scheduler/**/**/*
      - sw-utils/**/*
      - views/**/*
      - app.js
      - officials-schedule-export.js
      - ecosystem-dev.json
      - deploy/**/*
      - .gitlab-ci.yml
      - Dockerfile
  script:
    - ansible-playbook -l marc-aws-sw-dev deploy/node.yml
    - ansible-playbook -l marc-do-dev-sw-sw deploy/node.yml

deploy:node_dev_npm:
  stage: deploy
  only:
    refs:
      - development
    changes:
      - package.json
      - package-lock.json
  script:
    - ansible-playbook -l marc-aws-sw-dev deploy/node_npm_i.yml
    - ansible-playbook -l marc-do-dev-sw-sw deploy/node_npm_i.yml
  dependencies:
    - deploy:node_dev

reload:dev:
    stage: reload
    only:
        refs:
        - development
        changes:
        - package.json
        - package-lock.json
        - api/**/*
        - api/**/**/*
        - config/**/*
        - userconfig/routes/**/*
        - scheduler/**/*
        - scheduler/**/**/*
        - sw-utils/**/*
        - views/**/*
        - app.js
        - officials-schedule-export.js
        - ecosystem-dev.json
        - deploy/**/*
        - .gitlab-ci.yml
        - Dockerfile
    script:
      - ansible-playbook -l marc-aws-sw-dev deploy/pm2_reload.yml -e "REDIS_URL=$REDIS_URL_DEV" -e "HOST_PORT=$HOST_PORT_DEV" -e "EMAIL_REDIS_URL=$EMAIL_REDIS_URL_DEV" -e "SW_DB=$SW_DB_DEV" -e "NODE_ENV=development" -e "LOG_PG_CS=$LOG_PG_CS_DEV" -e "LOG_APP_ID=$LOG_APP_ID"
      - ansible-playbook -l marc-do-dev-sw-sw deploy/pm2_reload_dev.yml -e "REDIS_URL=$REDIS_URL_DEV" -e "HOST_PORT=$HOST_PORT_DEV" -e "EMAIL_REDIS_URL=$EMAIL_REDIS_URL_DEV" -e "SW_DB=$SW_DB_DEV" -e "NODE_ENV=development" -e "LOG_PG_CS=$LOG_PG_CS_DEV" -e "LOG_APP_ID=$LOG_APP_ID"
    dependencies:
      - deploy:node_dev
      - deploy:node_dev_npm

deploy:static_dev:
  stage: deploy
  only:
    refs:
      - development
    changes:
      - bower.json
      - .bowerrc
      - tasks/**/*
      - Gruntfile.js
      - assets/**/*
      - assets/**/**/*
      - frontend/**/*
      - frontend/**/**/*

  script:
    - "docker run --rm -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'npm i --legacy-peer-deps && bower install --allow-root;'"
    - "EMAIL_DB=$EMAIL_DB_DEV SW_DB=$SW_DB_DEV REDIS_URL=$REDIS_URL_DEV URLS=$URLS_DEV docker run --rm --env 'EMAIL_DB' --env 'SW_DB' --env 'REDIS_URL' --env 'URLS' -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'grunt dev'"
    - ansible-playbook -l marc-aws-sw-dev deploy/static.yml
    - "EMAIL_DB=$EMAIL_DB_DEV SW_DB=$SW_DB_DEV REDIS_URL=$REDIS_URL_DEV URLS=$URLS_DEV_DO docker run --rm --env 'EMAIL_DB' --env 'SW_DB' --env 'REDIS_URL' --env 'URLS' -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'grunt dev'"
    - ansible-playbook -l marc-do-dev-sw-sw deploy/static.yml
  cache:
    key:
      files:
        - package-lock.json
        - bower.json
    paths:
      - node_modules/
      - bower_components/

deploy:static_events_dev:
  stage: deploy
  only:
    refs:
      - development
    changes:
      - bower.json
      - .bowerrc
      - tasks/**/*
      - Gruntfile.js
      - assets/**/*
      - assets/**/**/*
      - frontend_event/**/*
      - frontend_event/**/**/*

  script:
    - "docker run --rm -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'npm i --legacy-peer-deps && bower install --allow-root;'"
    - "SW_DB=$SW_DB_DEV REDIS_URL=$REDIS_URL_DEV URLS=$URLS_DEV docker run --rm --env 'SW_DB' --env 'REDIS_URL' --env 'URLS' -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'grunt dev_esw;'"
    - ansible-playbook -l marc-aws-sw-dev deploy/static_esw.yml
    - "SW_DB=$SW_DB_DEV REDIS_URL=$REDIS_URL_DEV URLS=$URLS_DEV_DO docker run --rm --env 'SW_DB' --env 'REDIS_URL' --env 'URLS' -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'grunt dev_esw;'"
    - ansible-playbook -l marc-do-dev-sw-sw deploy/static_esw.yml
  cache:
    key:
      files:
        - package-lock.json
        - bower.json
    paths:
      - node_modules/
      - bower_components/

deploy:static_admin_dev:
  stage: deploy
  only:
    refs:
      - development
    changes:
      - bower.json
      - .bowerrc
      - tasks/**/*
      - Gruntfile.js
      - assets/**/*
      - assets/**/**/*
      - frontend_admin/**/*
      - frontend_admin/**/**/*

  script:
    - "docker run --rm -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'npm i --legacy-peer-deps && bower install --allow-root && npm rebuild node-sass;'"
    - "SW_DB=$SW_DB_DEV REDIS_URL=$REDIS_URL_DEV URLS=$URLS_DEV docker run --rm --env 'SW_DB' --env 'REDIS_URL' --env 'URLS' -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'grunt dev_asw;'"
    - ansible-playbook -l marc-aws-sw-dev deploy/static_asw_dev.yml
    - "SW_DB=$SW_DB_DEV REDIS_URL=$REDIS_URL_DEV URLS=$URLS_DEV_DO docker run --rm --env 'SW_DB' --env 'REDIS_URL' --env 'URLS' -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'grunt dev_asw;'"
    - ansible-playbook -l marc-do-dev-sw-sw deploy/static_asw_dev.yml
  cache:
    key:
      files:
        - package-lock.json
        - bower.json
    paths:
      - node_modules/
      - bower_components/

deploy:doc_dev:
  stage: deploy
  only:
      refs:
          - development
      changes:
          - userconfig/**/*
          - apidoc.json
  script:
      - "docker run --rm -v `pwd`:/build -w /build -u `id -u $USER`:`id -g $USER` --env HOME=. node:16 /bin/bash -c 'npm i --legacy-peer-deps && npm run doc;'"
      - ansible-playbook -l marc-aws-sw-dev deploy/doc_dev.yml
      - ansible-playbook -l marc-do-dev-sw-sw deploy/doc_dev.yml
  cache:
    key:
      files:
        - package-lock.json
        - bower.json
    paths:
      - node_modules/
      - bower_components/


deploy:node_dev2:
  stage: deploy
  only:
    - adv_e_module
  script:
    - ansible-playbook deploy/node_dev2.yml

deploy:static_dev2:
  stage: deploy
  only:
    - adv_e_module
  script:
    - "docker run --rm -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'npm i --legacy-peer-deps && bower install --allow-root && npm rebuild node-sass;'"
    - "EMAIL_DB=$EMAIL_DB_DEV SW_DB=$SW_DB_DEV docker run --rm --env 'EMAIL_DB' --env 'SW_DB' -v `pwd`:/build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'grunt dev;'"
    - ansible-playbook -l marc-aws-sw-dev deploy/static_dev2.yml
  cache:
    key:
      files:
        - package-lock.json
        - bower.json
    paths:
      - node_modules/
      - bower_components/

